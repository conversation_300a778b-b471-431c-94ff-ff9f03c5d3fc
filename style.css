/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap');

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    height: 100%;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

body {
    height: 100%;
    background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    overflow: hidden;
}

/* Animated background particles */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%);
    animation: float 20s ease-in-out infinite;
    pointer-events: none;
    z-index: -1;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-30px) rotate(120deg); }
    66% { transform: translateY(30px) rotate(240deg); }
}

/* Main wrapper */
.wrapper {
    perspective: 1000px;
}

/* Calculator container */
.calculator {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 24px;
    padding: 30px;
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.5),
        0 0 0 1px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transform: rotateX(5deg) rotateY(-5deg);
    transition: transform 0.3s ease;
    max-width: 350px;
    width: 100%;
}

.calculator:hover {
    transform: rotateX(0deg) rotateY(0deg) scale(1.02);
}

/* Display screen */
.display {
    background: linear-gradient(145deg, #1a1a2e, #0f0f23);
    color: #00ff88;
    font-size: 2.5rem;
    font-weight: 300;
    text-align: right;
    padding: 25px 20px;
    margin-bottom: 25px;
    border-radius: 16px;
    border: 1px solid rgba(0, 255, 136, 0.2);
    box-shadow:
        inset 0 2px 10px rgba(0, 0, 0, 0.3),
        0 0 20px rgba(0, 255, 136, 0.1);
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    font-family: 'Inter', monospace;
    letter-spacing: 1px;
    text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
    transition: all 0.3s ease;
}

.display:hover {
    box-shadow:
        inset 0 2px 10px rgba(0, 0, 0, 0.3),
        0 0 30px rgba(0, 255, 136, 0.2);
}

/* Button grid */
.buttons {
    display: grid;
    gap: 12px;
}

.buttons section {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
}

.buttons section:last-child {
    grid-template-columns: 2fr 1fr 1fr;
}

/* Button styles */
.btn {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    color: #ffffff;
    font-size: 1.2rem;
    font-weight: 500;
    font-family: 'Inter', sans-serif;
    padding: 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    backdrop-filter: blur(10px);
    box-shadow:
        0 4px 15px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

/* Button hover effects */
.btn:hover {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
    transform: translateY(-2px);
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        0 0 20px rgba(120, 119, 198, 0.3);
    border-color: rgba(120, 119, 198, 0.4);
}

.btn:active {
    transform: translateY(0px);
    box-shadow:
        0 2px 10px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Special button styles */
.btn:nth-child(1), /* C button */
.btn:nth-child(2)  /* Backspace button */ {
    background: linear-gradient(145deg, rgba(255, 99, 132, 0.2), rgba(255, 99, 132, 0.1));
    border-color: rgba(255, 99, 132, 0.3);
    color: #ff6384;
}

.btn:nth-child(1):hover,
.btn:nth-child(2):hover {
    background: linear-gradient(145deg, rgba(255, 99, 132, 0.3), rgba(255, 99, 132, 0.15));
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.3),
        0 0 20px rgba(255, 99, 132, 0.4);
    border-color: rgba(255, 99, 132, 0.5);
}

/* Operator buttons */
.btn:nth-child(3), /* Division */
.btn:nth-child(4), /* Multiplication */
.btn:nth-child(4), /* Subtraction */
.btn:nth-child(4)  /* Addition */ {
    background: linear-gradient(145deg, rgba(120, 119, 198, 0.2), rgba(120, 119, 198, 0.1));
    border-color: rgba(120, 119, 198, 0.3);
    color: #7877c6;
}

section:nth-child(1) .btn:nth-child(3),
section:nth-child(2) .btn:nth-child(4),
section:nth-child(3) .btn:nth-child(4),
section:nth-child(4) .btn:nth-child(4) {
    background: linear-gradient(145deg, rgba(120, 119, 198, 0.2), rgba(120, 119, 198, 0.1));
    border-color: rgba(120, 119, 198, 0.3);
    color: #7877c6;
}

section:nth-child(1) .btn:nth-child(3):hover,
section:nth-child(2) .btn:nth-child(4):hover,
section:nth-child(3) .btn:nth-child(4):hover,
section:nth-child(4) .btn:nth-child(4):hover {
    background: linear-gradient(145deg, rgba(120, 119, 198, 0.3), rgba(120, 119, 198, 0.15));
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.3),
        0 0 20px rgba(120, 119, 198, 0.4);
    border-color: rgba(120, 119, 198, 0.5);
}

/* Equals button */
section:last-child .btn:last-child {
    background: linear-gradient(145deg, rgba(0, 255, 136, 0.2), rgba(0, 255, 136, 0.1));
    border-color: rgba(0, 255, 136, 0.3);
    color: #00ff88;
    font-weight: 600;
}

section:last-child .btn:last-child:hover {
    background: linear-gradient(145deg, rgba(0, 255, 136, 0.3), rgba(0, 255, 136, 0.15));
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.3),
        0 0 20px rgba(0, 255, 136, 0.4);
    border-color: rgba(0, 255, 136, 0.5);
    text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
}

/* Zero button (spans 2 columns) */
section:last-child .btn:first-child {
    grid-column: span 2;
}

/* Ripple effect */
.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

.btn:active::before {
    width: 100px;
    height: 100px;
}

/* Responsive design */
@media (max-width: 480px) {
    .calculator {
        padding: 20px;
        margin: 10px;
    }

    .display {
        font-size: 2rem;
        padding: 20px 15px;
    }

    .btn {
        padding: 15px;
        font-size: 1.1rem;
    }
}

/* Accessibility improvements */
.btn:focus {
    outline: 2px solid rgba(120, 119, 198, 0.6);
    outline-offset: 2px;
}

/* Smooth transitions for all interactive elements */
* {
    transition: all 0.2s ease;
}