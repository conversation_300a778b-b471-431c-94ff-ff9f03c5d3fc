# 💕 Cute Calculator

A beautiful, girly-themed calculator with a soft pastel design and cute animations!

## ✨ Features

- **Girly Theme**: Soft pink and purple gradients with cute emojis
- **Smooth Animations**: Bouncy button effects and floating sparkles
- **Responsive Design**: Works on desktop and mobile devices
- **Full Functionality**: Basic arithmetic operations (+, -, ×, ÷)
- **Error Handling**: Prevents division by zero

## 🎨 Design Elements

- Floating hearts, sparkles, and butterflies in the background
- Glass morphism effects with soft shadows
- Cute decorative icons on buttons
- Elegant typography with Poppins and Dancing Script fonts
- Heartbeat animations and sparkle effects

## 🚀 Live Demo

[View Live Calculator](https://rjabwaad.github.io/cute-calculator/)

## 💻 Technologies Used

- HTML5
- CSS3 (with advanced animations and gradients)
- Vanilla JavaScript
- Google Fonts (Poppins & Dancing Script)

## 📱 How to Use

1. Click numbers to input values
2. Click operators (+, -, ×, ÷) for calculations
3. Press = to get results
4. Use C to clear everything
5. Use ← to delete last digit

## 🛠️ Installation

1. Clone this repository:
   ```bash
   git clone https://github.com/rjabwaad/cute-calculator.git
   ```
2. Open `index.html` in your browser
3. Enjoy your cute calculator! 💖

---

Made with 💕 by [rjabwaad](https://github.com/rjabwaad)
