<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="style.css">
    <title>Calculator</title>
</head>
<body>
    <div class="wrapper">
        <div class="calculator">
            <div class="display">0</div>
            <div class="buttons">
                <section>
                <button class="btn">C</button>
                <button class="btn">&larr;</button>
                <button class="btn">&divide;</button>
                </section>
                <section>
                <button class="btn">7</button>
                <button class="btn">8</button>
                <button class="btn">9</button>
                <button class="btn">&times;</button>
                </section>
                <section>
                <button class="btn">4</button>
                <button class="btn">5</button>
                <button class="btn">6</button>
                <button class="btn">&minus;</button>
                </section>
                <section>
                <button class="btn">1</button>
                <button class="btn">2</button>
                <button class="btn">3</button>
                <button class="btn">&plus;</button>
                </section>
                <section>
                <button class="btn">0</button>
                <button class="btn">&equals;</button>
                </section>
            </div>
        </div>
    </div>





    <script src="script.js"></script>
</body>
</html>