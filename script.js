let total = 0;
let buffer = '0';
let previousOperator = null;
const screen = document.querySelector('.display');
function buttonClick(value) {
    if (isNaN(value)) {
        handleSymbol(value);
    } else {
        handleNumber(value);
    }
    screen.innerText = buffer;
}
function handleNumber(value) {
    if (buffer === '0') {
        buffer = value;
    } else {
        buffer += value;
    }
}
function handleSymbol(value) {
    switch (value) {
        case 'C':
            buffer = '0';
            total = 0;
            break;
        case '&larr;':
            if (buffer.length === 1) {
                buffer = '0';
            } else {
                buffer = buffer.toString(0, buffer.length - 1);
            }
            break;
        case '=':
            if (previousOperator === null) {
                return;
            }
            flushOperation(parseInt(buffer));
            previousOperator = null;
            buffer = '' + total;
            total = 0;
            break;
        case '+':
        case '-':
        case 'x':
        case '÷':
            handleMath(value);
            break;
    }
}
function handleMath(value) {
    if (buffer === '0') {
        return;
    }
    const intBuffer = parseInt(buffer);
    if (total === 0) {
        total = intBuffer;
    } else {
        flushOperation(intBuffer);
    }
    previousOperator = value;
    buffer = '0';
}
function flushOperation(intBuffer) {
    if (previousOperator === '+') {
        total += intBuffer;
    } else if (previousOperator === '-') {
        total -= intBuffer;
    } else if (previousOperator === 'x') {
        total *= intBuffer;
    } else if (previousOperator === '÷') {
        total /= intBuffer;
    }
}
function handleNumber(value) {
    if (buffer === '0') {
        buffer = value;
    } else {
        buffer += value;
    }
}
function init() {
    document.querySelector('.calc-buttons')
        .addEventListener('click', function(event) {
            buttonClick(event.target.innerText);
        });
}
init();